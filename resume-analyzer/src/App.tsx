import { <PERSON><PERSON><PERSON>, Check<PERSON>ircle, Layers, TrendingUp, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

function App() {
  const [showCosmicBadge, setShowCosmicBadge] = useState(true)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-600 text-white">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-blue-900/80 backdrop-blur-md border-b border-white/10">
        <nav className="max-w-6xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Layers className="w-6 h-6 text-blue-400" />
            <span className="text-lg font-semibold">Resume Analyzer</span>
          </div>
          <div className="hidden md:flex items-center gap-8">
            <a href="#" className="text-white/80 hover:text-white transition-colors">Home</a>
            <a href="#" className="text-white/80 hover:text-white transition-colors">Pricing</a>
          </div>
          <Button className="bg-blue-600 hover:bg-blue-700">
            Sign In
          </Button>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="flex items-center justify-center min-h-screen px-6 pt-20">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6">
            Perfect your resume with{' '}
            <span className="text-blue-400">AI insights</span>
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            Get detailed AI-powered analysis of your resume. Improve your chances of
            landing your dream job with personalized feedback and ATS optimization.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
            >
              Get Started Free
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-white/20 bg-white/10 hover:bg-white/20 text-white px-8 py-3 text-lg backdrop-blur-sm"
            >
              View Pricing
              <ArrowRight className="ml-2 w-5 h-5 rotate-180" />
            </Button>
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-full max-w-6xl px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
              <CheckCircle className="w-8 h-8 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">AI-Powered Analysis</h3>
            <p className="text-white/70 text-sm">Get intelligent insights powered by GPT-4o</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
              <Layers className="w-8 h-8 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">ATS Optimization</h3>
            <p className="text-white/70 text-sm">Ensure your resume passes applicant tracking systems</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
              <TrendingUp className="w-8 h-8 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Targeted Feedback</h3>
            <p className="text-white/70 text-sm">Get specific suggestions for improvement</p>
          </div>
        </div>
      </section>

      {/* Built with Cosmic Badge */}
      {showCosmicBadge && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-full text-sm flex items-center gap-2 z-50">
          <span>Built with</span>
          <span className="font-bold">COSMIC</span>
          <button
            onClick={() => setShowCosmicBadge(false)}
            className="ml-2 hover:bg-green-600 rounded-full p-1 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  )
}

export default App
