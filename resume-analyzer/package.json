{"name": "resume-analyzer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^5.4.19"}}