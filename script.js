// Add smooth scrolling and interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Close cosmic badge functionality
    const closeBadge = document.querySelector('.close-badge');
    const cosmicBadge = document.querySelector('.cosmic-badge');
    
    if (closeBadge && cosmicBadge) {
        closeBadge.addEventListener('click', function() {
            cosmicBadge.style.display = 'none';
        });
    }
    
    // Add click handlers for buttons
    const getStartedBtn = document.querySelector('.btn-primary');
    const viewPricingBtn = document.querySelector('.btn-secondary');
    
    if (getStartedBtn) {
        getStartedBtn.addEventListener('click', function() {
            alert('Get Started Free clicked! This would typically redirect to a signup page.');
        });
    }
    
    if (viewPricingBtn) {
        viewPricingBtn.addEventListener('click', function() {
            alert('View Pricing clicked! This would typically show pricing information.');
        });
    }
    
    // Add parallax effect to background
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero');
        if (parallax) {
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        }
    });
    
    // Add hover effects to feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
